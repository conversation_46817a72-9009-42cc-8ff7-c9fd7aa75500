import com.android.build.api.variant.impl.VariantOutputImpl
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.Properties
import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
	alias(libs.plugins.android.application)
	alias(libs.plugins.kotlin.android)
	alias(libs.plugins.sentry)
	alias(libs.plugins.ksp)
	alias(libs.plugins.compose.compiler)
}

ksp {
	arg("room.schemaLocation", "$projectDir/schemas")
}

val Project.localPropertiesFile: File get() = project.rootProject.file("local.properties")
fun Project.getLocalProperty(key: String): String {
	return if (localPropertiesFile.exists()) {
		val properties = Properties()
		localPropertiesFile.inputStream().buffered().use { input ->
			properties.load(input)
		}
		properties.getProperty(key)
	} else {
		localPropertiesFile.createNewFile()
		""
	}
}

android {
	namespace = "com.fjdynamics.towermanager"
	compileSdk = 35

	defaultConfig {
		applicationId = "com.fjdynamics.towermanager"
		minSdk = 28
		targetSdk = 35
		versionCode = 6
		versionName = "********"

		testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

		// PostHog configuration from local.properties
		buildConfigField(
			"String",
			"POSTHOG_API_KEY",
			getLocalProperty("posthog_api_key"),
		)
		buildConfigField(
			"String",
			"POSTHOG_HOST",
			getLocalProperty("posthog_host"),
		)
	}

	signingConfigs {
		getByName("debug") {
			storeFile = file("../fjd.jks")
			storePassword = "fj171216"
			keyAlias = "fj"
			keyPassword = "fj171216"
		}
		create("system") {
			//从平平板系统签名
			storeFile = file("../platform.keystore")
			storePassword = "123456"
			keyAlias = "platform"
			keyPassword = "123456"
		}
	}

	buildTypes {
		debug {
			isMinifyEnabled = false
			proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
			signingConfig = signingConfigs.getByName("debug")
		}
		release {
			isMinifyEnabled = false
			proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
			signingConfig = signingConfigs.getByName("system")
		}
	}

	flavorDimensions += "version"
	productFlavors {
		create("qa") {
			isDefault = true
		}
		create("prod") {
		}
	}

	configurations {
		configureEach {
			exclude(group = "io.sentry", module = "sentry-android-timber")
		}
	}

	compileOptions {
		sourceCompatibility = JavaVersion.VERSION_17
		targetCompatibility = JavaVersion.VERSION_17
	}

	kotlin {
		compilerOptions {
			jvmTarget.set(JvmTarget.JVM_17)
			javaParameters.set(true)
		}
	}

	buildFeatures {
		viewBinding = true
		buildConfig = true
		compose = true
	}
}

androidComponents.onVariants { variant ->
	val flavorName = variant.flavorName ?: ""
	val buildType = variant.buildType ?: ""
	val timestamp by lazy {
		SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
	}

	variant.outputs.forEach { output ->
		(output as? VariantOutputImpl)?.apply {
			outputFileName.set(
				"TowerManager_pad_${versionName}_${flavorName}_${buildType}_${timestamp}.apk",
			)
		}
	}
}

dependencies {
	implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar", "*.aar"))))
	implementation(libs.bundles.androidx.core)
	implementation(libs.material)
	implementation(libs.mmkv)
	implementation(libs.bundles.room)
	ksp(libs.androidx.room.compiler)
	implementation(libs.bundles.retrofit)
	implementation(platform(libs.okhttp.bom))
	implementation(libs.bundles.okhttp)
	implementation(libs.utilcode)
	implementation(libs.glide)
	implementation(libs.eventbus)
	implementation(libs.commons.lang3)
	implementation(libs.autosize)
	implementation(libs.bundles.logging)
	implementation(libs.brvah)
	implementation(libs.bundles.smart.refresh)
	implementation(libs.posthog)
	implementation(libs.photoview)
	implementation(libs.bundles.media3)
	implementation(libs.rtsp.client)
	implementation(platform(libs.koin.bom))
	implementation(libs.koin.android)
	implementation(libs.koin.android.compat)
	implementation(platform(libs.androidx.compose.bom))
	implementation(libs.bundles.androidx.compose)

	testImplementation(libs.bundles.testing)
	androidTestImplementation(libs.bundles.android.testing)
}

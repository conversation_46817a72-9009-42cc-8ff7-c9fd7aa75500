package com.fjdynamics.towermanager

import android.app.Application
import android.text.TextUtils
import androidx.media3.common.util.UnstableApi
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import com.alexvas.utils.MediaCodecUtils
import com.fjdynamics.towermanager.di.getAppModule
import com.fjdynamics.towermanager.util.Analytics
import com.fjdynamics.towermanager.util.AnalyticsEvent
import com.fjdynamics.towermanager.util.AppStartupTasks
import com.fjdynamics.towermanager.util.DeviceUtil
import com.fjdynamics.towermanager.util.StartupTimeMonitor
import com.fjdynamics.towermanager.util.StepName
import com.fjdynamics.towermanager.util.logger
import com.fjdynamics.towermanager.util.recordEvent
import com.tencent.mmkv.MMKV
import java.io.File
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin

/**
 * 司机中控平板App
 *
 * <AUTHOR>
 * @since 2025/4/25
 */
@UnstableApi
class App : Application() {

	override fun onCreate() {
		super.onCreate()
		instance = this
		val startupTimeMonitor = StartupTimeMonitor()
		AppStartupTasks.initializeLogger(this)
		startupTimeMonitor.mark(StepName.Logging)

		MMKV.initialize(this)
		startupTimeMonitor.mark(StepName.Caching)

		val defaultUEH = Thread.getDefaultUncaughtExceptionHandler()
		Thread.setDefaultUncaughtExceptionHandler { t, e ->
			logger<App>().error("!!!FATAL EXCEPTION!!! ($e)", e)
			Thread.sleep(500)
			defaultUEH?.uncaughtException(t, e)
		}
		startupTimeMonitor.mark(StepName.UncaughtExceptionHandler)

		val scope = createAppRootCoroutineScope()

		startKoin {
			androidContext(this@App)
			modules(getAppModule())
		}
		startupTimeMonitor.mark(StepName.Modules)

		val analyticsInitializer = scope.launch {
			AppStartupTasks.initializeAnalytics(this@App, BuildConfig.POSTHOG_API_KEY, BuildConfig.POSTHOG_HOST)
		}

		scope.launch {
			//只统计在主线程初始化的步骤耗时，在协程中初始化并且不需要join的任务不统计耗时
			MediaCodecUtils.getHardwareDecoders("video/avc")
			MediaCodecUtils.getHardwareDecoders("video/hevc")
		}

		runBlocking { analyticsInitializer.join() }
		startupTimeMonitor.mark(StepName.Analytics)

		Analytics.recordEvent(AnalyticsEvent.AppStart) {
			putAll(startupTimeMonitor.getMarks())
			put("total_time", startupTimeMonitor.getTotalDuration().inWholeMilliseconds)
		}
	}

	companion object {
		lateinit var instance: App

		val simpleCache by lazy {
			val cacheSize = 100 * 1024 * 1024L // 100MB
			val cacheEvictor = LeastRecentlyUsedCacheEvictor(cacheSize)
			val databaseProvider = StandaloneDatabaseProvider(instance)
			SimpleCache(File(instance.cacheDir, "media"), cacheEvictor, databaseProvider)
		}

		@JvmField
		val IS_PROD: Boolean = TextUtils.equals(BuildConfig.FLAVOR, "prod")

		@JvmField
		val DEVICE_SN: String = DeviceUtil.getVehicleSn()

		private const val POSTHOG_API_KEY = "phc_1j0FjoLg23RzwFnDlHKlFSSuFPaIasirrzK1P9WH97B"
		private const val POSTHOG_HOST = "https://us.i.posthog.com"
	}

}

fun createAppRootCoroutineScope(): CoroutineScope {
	return CoroutineScope(
		CoroutineExceptionHandler { coroutineContext, throwable ->
			logger<App>().warn("Uncaught exception in coroutine $coroutineContext", throwable)
		} + SupervisorJob() + Dispatchers.Default,
	)
}
